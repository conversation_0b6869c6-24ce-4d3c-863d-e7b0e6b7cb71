from api.models.response import StandardResponse
from api.models.xml_infer import parsePageRequest, parsePageResponse
from api.services.post_service import post_annotation_xml
from api.services.xml_infer import parse_page_service
from fastapi import APIRouter, BackgroundTasks, Header

router = APIRouter()


@router.post("/parse_page", response_model=StandardResponse[parsePageResponse])
async def parse_page(
    request: parsePageRequest,
    background_tasks: BackgroundTasks,
    traceparent: str = Header("default", alias="traceparent"),
):
    instance = parse_page_service(
        app_id=request.app_id,
        parser_url=request.parser_url,
        screen_url=request.screen_url,
        dom_xml=request.dom_xml,
        elements_rects=request.elements_rects,
        native_elements_rects=request.native_elements_rects,
        extra_config=request.extra_config,
        trace_id=traceparent,
    )
    xml_info = await instance.run()
    # when data come，we trans it into post annotation
    if traceparent and traceparent != "default":
        try:
            trace_id = traceparent.split("-")[1]
        except Exception:
            trace_id = traceparent
        background_tasks.add_task(
            post_annotation_xml,
            request.app_id,
            request.screen_url,
            xml_info,
            trace_id,
        )
    return StandardResponse(data=xml_info)
