from typing import Optional

from pydantic import BaseModel, Field


class ExtraConfig(BaseModel):
    """XML推理服务的额外配置"""

    disable_omniparser: bool = Field(
        default=False,
        description="是否禁用 omniparser 服务，禁用后将不会调用 omniparser 进行文本识别",
    )

    disable_xpath_summary: bool = Field(
        default=False,
        description="是否禁用 xpath 摘要功能，禁用后将不会从 KV 存储中获取 xpath 对应的摘要信息",
    )

    xpath_summary_prefix: Optional[str] = Field(
        default=None, description="xpath 摘要的前缀，用于在 KV 存储中查找对应的摘要信息"
    )


class parsePageResponse(BaseModel):
    # XMLInfoDTO
    xml_data: str
    idx_to_xpath: dict[int, str]
    xpath_to_idx: dict[str, int]
    screen: str
    original_screen: str
    log_screen: str
    idx_to_rect: dict[int, dict]


class parsePageRequest(BaseModel):
    app_id: str
    parser_url: str
    screen_url: str
    dom_xml: str
    elements_rects: list
    native_elements_rects: list
    extra_config: Optional[ExtraConfig] = None
