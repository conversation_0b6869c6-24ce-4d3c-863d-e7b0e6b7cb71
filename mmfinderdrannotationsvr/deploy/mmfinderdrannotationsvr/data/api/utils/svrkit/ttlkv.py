import asyncio
import concurrent.futures
import json
import time
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Optional

import httpx
import requests
from api.utils.wecube.log import log
from svrkit.core.oss import OssAttrInc

TTLKV_BASE_URL = "http://mmfinderdrassistanttestsvr.prod.polaris:19542"
TTLKV_HOST = "www.mmfinderdrassistanttestsvr.routesvr.wx.com"
TTLKV_EXPIRE_SECONDS = int(86400 * 365)


def generate_key(key: str) -> str:
    return f"wx_mini_annotation_v2_{key}"


class TTLKVClient:
    def __init__(
        self,
        base_url: str = TTLKV_BASE_URL,
        host: str = TTLKV_HOST,
        expire_seconds: int = TTLKV_EXPIRE_SECONDS,
        max_workers: int = 5,
    ):
        self.base_url = base_url
        self.host = host
        self.expire_seconds = expire_seconds
        self.headers = {
            "host": host,
            "Content-Type": "application/json",
            "Svrkit_Req_Base64": "0",
            "Svrkit_Resp_Base64": "0",
        }
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

    def get(self, key: str, context: Dict = None) -> str:
        """同步获取TTLKV数据"""
        try:
            url = f"{self.base_url}/GetKvItem"
            data = {"key": key}
            response = requests.post(url, headers=self.headers, json=data, timeout=5)
            if response.status_code != 200:
                print(f"Failed to get data from TTLKV: {response.text}")
                return ""
            value = self._parse_response(response.content)
            return value
        except Exception as e:
            print(f"Error getting TTLKV data: {e}")
            return ""

    def set(
        self, key: str, value: str, expire_seconds: int = None, context: Dict = None
    ) -> bool:
        """同步设置TTLKV数据

        Args:
            key: TTLKV键名
            value: 要存储的值
            expire_seconds: 过期时间（秒）
            context: 包含时间信息的上下文

        Returns:
            bool: 是否设置成功
        """
        if expire_seconds is None:
            expire_seconds = self.expire_seconds

        # 没有上下文则创建空字典
        if context is None:
            context = {}

        # 记录TTLKV写入开始 - 新增日志
        print(
            f"开始TTLKV写入 - key: {key}, 数据大小: {len(value)} 字节, 过期时间: {expire_seconds}秒"
        )

        # 记录TTLKV写入开始时间
        ttlkv_write_start_time = time.time()

        try:
            url = f"{self.base_url}/SetKvItem"
            data = {
                "key": key,
                "value": value,
                "expire_timestamp": expire_seconds,
            }

            # 记录请求信息 - 新增日志
            print(f"发送TTLKV请求 - URL: {url}, key: {key}, headers: {self.headers}")
            for retry_idx in range(3):
                try:
                    response = requests.post(
                        url, headers=self.headers, json=data, timeout=5
                    )
                    if response.status_code == 200:
                        break
                    print(
                        f"[acluckywang][retry:{retry_idx}] SetKvItem fail, http_status:{repr(response.status_code)}"
                    )
                except Exception as e:
                    print(f"[acluckywang][retry:{retry_idx}] SetKvItem fail, {repr(e)}")
            if response.status_code != 200:
                print(
                    f"[acluckywang][final] SetKvItem fail, http_status:{repr(response.status_code)}"
                )
                raise ValueError(
                    f"set kv fail, http_status_code:{repr(response.status_code)}"
                )

            json_result = response.json()
            if json_result.get("ret_code", 0) != 0:
                print(f"[acluckywang] SetKvItem fail, {repr(json_result)}")
                raise ValueError(f"set kv fail, {repr(json_result)}")
            # 记录TTLKV写入结束时间
            ttlkv_write_end_time = time.time()

            # 计算TTLKV写入耗时（毫秒）
            ttlkv_write_time_ms = int(
                (ttlkv_write_end_time - ttlkv_write_start_time) * 1000
            )
            # 转换为字符串
            ttlkv_write_time_ms_str = str(ttlkv_write_time_ms)

            # 记录响应信息 - 新增日志
            print(
                f"收到TTLKV响应 - 状态码: {response.status_code}, 耗时: {ttlkv_write_time_ms_str}ms, 响应内容: {response.text[:100]}..."
            )

            # 获取上下文中的时间信息
            context.get("consumption_time", 0)
            context.get("processing_start_time", 0)
            context.get("llm_timestamp", 0)
            context.get("pulsar_publish_time", 0)

            if response.status_code >= 400:
                print(f"TTLKV写入失败: {response.text}, key: {key}")
                return False

            print(f"TTLKV写入成功 - key: {key}, 数据大小: {len(value)} 字节")
            return True
        except Exception as e:
            print(f"TTLKV写入异常: {e}, key: {key}")
            return False

    async def async_get(
        self, key: str, use_cache: bool = False, context: Dict = None
    ) -> str:
        """异步获取TTLKV数据"""
        if use_cache and key in self._cache:
            # 上报缓存命中
            if self.enable_report:
                self.wecube_reporter.report_ttlkv_get(
                    key, True, len(self._cache[key]), from_cache=True
                )
            return self._cache[key]

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self.get, key, False)

    async def async_set(
        self, key: str, value: str, expire_seconds: int = None, context: Dict = None
    ) -> bool:
        """异步设置TTLKV数据"""
        if expire_seconds is None:
            expire_seconds = self.expire_seconds

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor, self.set, key, value, expire_seconds, context
        )

    def _parse_response(self, content: bytes) -> str:
        """解析TTLKV响应内容"""
        try:
            if not content:
                return ""

            resp = content.decode("utf-8")
            body_dict = json.loads(resp)
            value = body_dict["kv_item"]["value"]
            return value
        except Exception as e:
            print(f"Error parsing TTLKV response: {e}")
            return ""

    def batch_get(
        self, keys: List[str], use_cache: bool = False, context: Dict = None
    ) -> Dict[str, str]:
        """批量获取TTLKV数据"""
        result = {}
        for key in keys:
            value = self.get(key, use_cache, context)
            result[key] = value

        return result

    def batch_set(
        self, data: Dict[str, str], expire_seconds: int = None, context: Dict = None
    ) -> Dict[str, bool]:
        """批量设置TTLKV数据"""
        result = {}
        for key, value in data.items():
            success = self.set(key, value, expire_seconds, context)
            result[key] = success

        return result


ttlkv_client = TTLKVClient()


def get_ttlkv(key: str) -> str:
    return ttlkv_client.get(key)


def set_ttlkv(
    key: str, value: str, expire_seconds: int = None, context: Dict = None
) -> bool:
    return ttlkv_client.set(key, value, expire_seconds, context)


def get_sum_ttlkv(app_id: str, xpath: str) -> str:
    key = f"wx_mini_annotation_xpath_summary_{app_id}_{xpath}"
    value = get_ttlkv(key)
    return value if value is not None else ""


def batch_get_sum_ttlkv(
    app_id: str, xpaths: list[str], prefix: Optional[str] = None
) -> dict[str, str]:
    OssAttrInc(535937, 50, 1)
    if prefix is None:
        prefix = "wx_mini_annotation_xpath_summary_"
    xm_values = {}
    with ThreadPoolExecutor() as executor:
        futures = {
            executor.submit(get_ttlkv, f"{prefix}{app_id}_{xpath}"): xpath
            for xpath in xpaths
        }
        for future in concurrent.futures.as_completed(futures):
            xpath = futures[future]
            try:
                xm_values[xpath] = future.result()
            except Exception as e:
                OssAttrInc(535937, 48, 1)
                print(f"Error Batch getting TTLKV for xpath {xpath}: {e}")
                log(f"Error Batch getting TTLKV for xpath {xpath}: {e}")
                xm_values[xpath] = ""
    return xm_values


async def async_batch_get_sum_ttlkv(
    app_id: str, xpaths: list[str], prefix: Optional[str] = None
) -> dict[str, str]:
    if prefix is None:
        prefix = "wx_mini_annotation_xpath_summary_"
    xm_values = {}

    async def fetch_single_value(xpath):
        key = f"{prefix}{app_id}_{xpath}"
        try:
            value = await asyncio.to_thread(get_ttlkv, key)
            return xpath, value
        except Exception as e:
            OssAttrInc(535937, 48, 1)
            print(f"Error Batch getting TTLKV for xpath {xpath}: {e}")
            log(f"Error Batch getting TTLKV for xpath {xpath}: {e}")
            return xpath, ""

    results = await asyncio.gather(*[fetch_single_value(xpath) for xpath in xpaths])
    for xpath, value in results:
        xm_values[xpath] = value
    return xm_values


async def async_batch_get_sum_ttlkv_v2(
    app_id: str, xpaths: list[str], prefix: Optional[str] = None
) -> dict[str, str]:
    if prefix is None:
        prefix = "wx_mini_annotation_xpath_summary_"
    xm_values = {}
    # httpx 异步 POST http://mmfinderdrminiragsvr.polaris:8080/tool/batch_get {"keys":{prefix}{app_id}_{xpath} for xpath in xpaths}
    OssAttrInc(535937, 70, 1)
    OssAttrInc(535937, 71, len(xpaths))
    async with httpx.AsyncClient() as client:
        key_to_xpath = {prefix + app_id + "_" + xpath: xpath for xpath in xpaths}
        keys = list(key_to_xpath.keys())
        response = await client.post(
            "http://mmfinderdrminiragsvr.polaris:8080/tool/batch_get",
            json={"keys": keys},
            timeout=60,
        )
        if response.status_code != 200:
            OssAttrInc(535937, 72, 1)
            return {}
        results = response.json()
        values = results.get("data", {}).get("values", {})
        for full_key, value in values.items():
            if full_key in key_to_xpath:
                xpath = key_to_xpath[full_key]
                xm_values[xpath] = value
        OssAttrInc(535937, 73, 1)
        return xm_values
