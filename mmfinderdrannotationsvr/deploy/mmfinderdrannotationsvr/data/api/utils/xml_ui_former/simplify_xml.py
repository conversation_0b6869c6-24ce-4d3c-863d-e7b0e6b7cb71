import json
import re
import sys
import xml.etree.ElementTree as ET
from itertools import combinations

from .convert_wechat_minihtml_to_xml import convert as wxconvert
from .element_utils import (
    get_lca,
    gettext,
    illegal_node,
    is_crucial_node,
    make_view,
    to_str,
)


def add_attributes_to_tabbar_items(input_str):
    # 使用正则表达式匹配所有tabbar-item标签
    pattern = re.compile(r"(<tabbar-item\b[^>]*>)")

    # 查找所有匹配的tabbar-item标签
    matches = pattern.finditer(input_str)

    # 记录当前处理的索引位置
    last_pos = 0
    output_parts = []
    index = 0

    for match in matches:
        # 添加匹配前的部分
        output_parts.append(input_str[last_pos : match.start()])

        # 获取原始标签
        original_tag = match.group(1)

        # 添加属性
        if "index=" not in original_tag:
            modified_tag = original_tag.rstrip(">") + f' index="{index}"'
        else:
            modified_tag = original_tag

        if "event=" not in modified_tag:
            modified_tag = modified_tag.rstrip(">") + f" event=\"['tap']\""

        modified_tag += ">"

        output_parts.append(modified_tag)
        last_pos = match.end()
        index += 1

    # 添加剩余部分
    output_parts.append(input_str[last_pos:])

    return "".join(output_parts)


def process_accessibility_tree(root):
    reduced_elements = []

    def dfs_tree(node, parent=None, node_order="", dep=0):
        if illegal_node(node):
            return

        current_text, current_desc = gettext(node)

        # 只要有文本/描述，或者是关键节点，都单独加入 reduced_elements
        if (
            (current_text or current_desc)
            or is_crucial_node(node)
            or node.attrib.get("typeable") == "true"
        ):
            node_view = make_view(node)
            node_view.update({"id": node_order})
            reduced_elements.append(node_view)

        # 递归处理子节点
        for idx, child in enumerate(node):
            dfs_tree(child, node, node_order + f"{idx},", dep + 1)

    dfs_tree(root)
    return [], reduced_elements


def merge_span(elements):
    """
    将元素列表中所有span元素的文本，向上聚合到第一个非span元素的text字段中
    """
    if not elements:
        return []
    span_texts = []
    n = len(elements)
    for i in range(n - 1, -1, -1):
        element = elements[i]
        if "span" in elements[i]["class"]:
            elements.pop(i)
            if text := element.get("text", "").strip():
                span_texts.append(text)
        elif span_texts:
            element["text"] = (
                element.get("text", "") + " ".join(span_texts[::-1]).strip()
            )
            span_texts = []
    return elements


def parameterize_actions(elements, root):
    last_pos = 0
    output = []
    action_nodes = {el["id"]: idx for idx, el in enumerate(elements)}

    target_elements = elements

    def dfs1(node, node_order=""):
        if illegal_node(node):
            return

        if node_order in action_nodes:
            return

        if not gettext(node) == ("", ""):
            node_view = {"id": node_order}
            target_elements.append(node_view)

        for id, child in enumerate(node):
            dfs1(child, node_order + f"{id},")

    dfs1(root)

    lca_set = set(el["id"] for el in target_elements)
    for el1, el2 in combinations(target_elements, 2):
        lca = get_lca(el1, el2)
        if not lca == "":
            lca_set.add(lca)

    xpath_map = {}
    xpath_to_text = {}

    def dfs2(node, node_order="", indent=""):
        if illegal_node(node):
            return

        if node_order in action_nodes:
            index = action_nodes[node_order]
            output.append(
                indent + f"[{last_pos + index}] " + to_str(elements[last_pos + index])
            )
            indent += " "
            xpath_map[index] = node.attrib["xpath"]
            xpath_to_text[node.attrib["xpath"]] = node.get("text", "")

        elif node_order in lca_set:
            node_view = make_view(node)
            node_view["actions"] = set()
            output.append(indent + to_str(node_view))
            indent += " "
            xpath_to_text[node.attrib["xpath"]] = node.get("text", "")

        for id, child in enumerate(node):
            dfs2(child, node_order + f"{id},", indent)

    dfs2(root)

    return "\n".join(output), xpath_map, xpath_to_text


if __name__ == "__main__":
    raw_html = sys.stdin.buffer.read().decode("utf-8")
    xml = wxconvert(raw_html)

    root = ET.fromstring(xml)
    _, elements = process_accessibility_tree(root)
    screen, xpath, _ = parameterize_actions(elements, root)
    index_to_xpath = {path: idx for idx, path in xpath.items()}
    result = {"screen": screen, "indexToXpath": xpath, "xpathToIndex": index_to_xpath}
    sys.stdout.buffer.write(json.dumps(result, ensure_ascii=False).encode("utf-8"))
    # print(xpath)
    # for val in xpath:
    #     print(xpath[val])
    # print(screen)
    sys.stdout.flush()
    # with open("output.txt", "w") as f:
    #     f.write(screen + "\n\n")
    #     f.write(str(xpath))
